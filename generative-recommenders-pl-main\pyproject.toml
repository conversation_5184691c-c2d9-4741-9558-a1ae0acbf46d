[project]
name = "generative-recommenders-pl"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "torch==2.3.1",
    "numpy>=1.26.4,<2.0.0",
    "hypothesis==6.108.4",
    "pandas>=2.2.2,<2.3.0",
    "iopath==0.1.10",
    "lightning>=2.4.0",
    "hydra-core>=1.3.2",
    "hydra-colorlog>=1.2.0",
    "rich>=13.7.1",
    "tensorboard==2.16.1",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "black",
    "pre-commit",
]

test = [
    "pytest",
    "coverage",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# not working properly with hydra config
# [project.scripts]
# gr_train = "generative_recommenders_pl.scripts.train:main"
# gr_eval = "generative_recommenders_pl.scripts.eval:main"
# gr_prepare_data = "generative_recommenders_pl.scripts.prepare_data:main"
