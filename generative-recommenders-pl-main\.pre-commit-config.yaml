fail_fast: true

exclude: |
  (?x)^(
    .*/(snapshots)/.*|
  )$

repos:
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.19
    hooks:
      - id: validate-pyproject

  - repo: https://github.com/crate-ci/typos
    rev: v1.24.1
    hooks:
      - id: typos

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, json5]

  - repo: https://github.com/pre-commit/mirrors-isort
    rev: v5.10.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.2
    hooks:
      - id: ruff-format
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  # md formatting
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.17
    hooks:
      - id: mdformat
        args: ["--number"]
        additional_dependencies:
          - mdformat-gfm
          - mdformat-tables
          - mdformat_frontmatter
          # - mdformat-toc
          # - mdformat-black
