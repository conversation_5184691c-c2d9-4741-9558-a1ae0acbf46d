_target_: lightning.pytorch.trainer.Trainer

default_root_dir: ${paths.output_dir}

min_epochs: 1 # prevents early stopping
max_epochs: 10

accelerator: cpu
devices: 1

# mixed precision for extra speed-up
# precision: 16

# perform a validation loop every N training epochs
check_val_every_n_epoch: 1

# set True to to ensure deterministic results
# makes training slower but gives more reproducibility than just setting seeds
deterministic: False

# set limit for the training and validation loops
limit_train_batches: 1.0
limit_val_batches: 1.0
