import abc
from typing import Dict, <PERSON>ple

import torch


class InputFeaturesPreprocessorModule(torch.nn.Module):
    @abc.abstractmethod
    def debug_str(self) -> str:
        pass

    @abc.abstractmethod
    def forward(
        self,
        past_lengths: torch.Tensor,
        past_ids: torch.Tensor,
        past_embeddings: torch.Tensor,
        past_payloads: Dict[str, torch.Tensor],
    ) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor | None]:
        pass
